# Fogsight [**English**](./readme_en.md) | [**中文**](./readme.md)

<p align="center">
  <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/logos/fogsight_logo_white_bg.png"
       alt="Fogsight Logo"
       width="300">
</p>

**Fogsight is an animation‑generation agent powered by large‑language models (LLMs).  
Give it an abstract concept or a single word and it will transform it into a high‑quality, vivid animation.**

Once Fogsight is deployed locally, simply enter a word and click **Generate** to watch the animation appear.

<p align="center">
  <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/1.png"
       alt="UI Screenshot"
       width="550">
</p>

We designed an intuitive *Language User Interface* (LUI) so you can **easily edit or refine the generated animation—“say it and it happens.”**

*Fogsight* means **“a concrete shape within fuzzy intelligence.”**

## Animation Samples

Below are animations generated by Fogsight AI. Click any thumbnail to view the video.

<table>
  <tr>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1PXgKzBEyN">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/entropy_increase_thumbnail.png" width="350"><br>
        <strong>The Law of Increasing Entropy (Physics)</strong><br>
        <em>Input: 熵增定律</em>
      </a>
    </td>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1yXgKzqE42">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/euler_formula_thumbnail.png" width="350"><br>
        <strong>Euler’s Polyhedron Formula (Mathematics)</strong><br>
        <em>Input: 欧拉定理</em>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1sQgKzMEox">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/bubble_sort_thumbnail.png" width="350"><br>
        <strong>Bubble Sort (Computer Science)</strong><br>
        <em>Input: 冒泡排序</em>
      </a>
    </td>
    <td align="center">
      <a href="https://www.bilibili.com/video/BV1yQgKzMEo6">
        <img src="https://github.com/hamutama/caimaopics/raw/main/fogsight/thumbnails/affordance_thumbnail.png" width="350"><br>
        <strong>Affordance (Design)</strong><br>
        <em>Input: affordance in design</em>
      </a>
    </td>
  </tr>
</table>

## Key Features

* **Concept‑to‑Vision** – Enter a topic and Fogsight delivers a complete narrative animation with bilingual narration and cinematic visuals.  
* **Intelligent Orchestration** – An LLM drives the entire pipeline. From script to visual elements and motion, the AI composes everything in a single pass.  
* **Language User Interface (LUI)** – Iterate and fine‑tune through multi‑turn dialogue until the artwork matches your vision perfectly.  

## Quick Start

### Requirements

* Python 3.9+  
* A modern web browser (Chrome, Firefox, Edge, …)  
* An LLM API key. We recommend **Google Gemini 2.5**. The current version uses an OpenAI‑compatible SDK, so you can call Gemini via OpenRouter, One API or another proxy service.  

### Installation & Run

1. **Clone the repository**
   ```bash
   git clone https://github.com/fogsightai/fogsight.git
   cd fogsight
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

3. **Configure your API key**

   ```bash
   cp .env.example .env
   # Edit .env and fill in API_KEY and BASE_URL
   ```
    ```bash
   cp demo-credentials.json credentials.json
   # Edit credentials.json and fill in API_KEY 和 BASE_URL。
   ```

4. **One‑click launch**

   ```bash
   python start_fogsight.py
   # Your browser will open at http://127.0.0.1:8000
   ```

5. **Create!**
   Enter a topic (e.g. “Bubble Sort”) in the page and wait for the result.

## Contact / Community

Join the discussion via **[this link](https://fogsightai.feishu.cn/wiki/WvODwyUr1iSAe0kEyKfcpqvynGc?from=from_copylink)**.

## Contributors

### Academia

* [@taited](https://taited.github.io/) – PhD Student, CUHK (Shenzhen)
* [@yjydya](https://github.com/ydyjya) – PhD Student, Nanyang Technological University

### WaytoAGI Community

* [@richkatchen / 陈财猫](https://okjk.co/enodyA)
* [@kk](https://okjk.co/zC8myE)

### Index Future Lab

* [Lin He](https://github.com/zerohe2001)

### AI Explorers

* [Xiaodao Huang](https://okjk.co/CkFav6)

### Independent Developers & AI Artists

* [@shuyan-5200](https://github.com/shuyan-5200)
* [Ruyue Wang](https://github.com/Moonywang)
* [@Jack‑the‑Builder](https://github.com/Jack-the-Builder)
* [@xiayurain95](https://github.com/xiayurain95)
* [@Lixin Cai](https://github.com/Lixin-Cai)

## License

This project is released under the **MIT License**.
If you cite this project with attribution and a link back, we’ll be very grateful 😊.

*Fogsight is part of the WaytoAGI open‑source programme — empowering more people through AI.*
