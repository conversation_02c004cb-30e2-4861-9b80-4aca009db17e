#!/usr/bin/env python3
import google.generativeai as genai
import json

def test_gemini_api():
    try:
        # 读取API密钥
        credentials = json.load(open('credentials.json'))
        API_KEY = credentials['API_KEY']
        
        print(f"使用API密钥: {API_KEY[:10]}...")
        
        # 配置API
        genai.configure(api_key=API_KEY)
        
        # 测试API调用 - 尝试不同的模型名称
        try:
            model = genai.GenerativeModel('gemini-1.5-pro')
            response = model.generate_content('Hello, 请用中文回复一句话测试')
        except Exception as e1:
            print(f"尝试 gemini-1.5-pro 失败: {e1}")
            try:
                model = genai.GenerativeModel('gemini-pro')
                response = model.generate_content('Hello, 请用中文回复一句话测试')
            except Exception as e2:
                print(f"尝试 gemini-pro 失败: {e2}")
                raise e2
        
        print('✅ API测试成功!')
        print('响应:', response.text)
        return True
        
    except Exception as e:
        print('❌ API测试失败:', str(e))
        return False

if __name__ == "__main__":
    test_gemini_api()
