#!/usr/bin/env python3
from google import genai
import json

def test_gemini_api():
    try:
        # 读取API密钥
        credentials = json.load(open('credentials.json'))
        API_KEY = credentials['API_KEY']

        print(f"使用API密钥: {API_KEY[:10]}...")

        # 创建客户端
        client = genai.Client(api_key=API_KEY)

        # 测试API调用
        print("正在测试 gemini-2.0-flash 模型...")
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents="Hello, 请用中文回复一句话测试"
        )

        print('✅ API测试成功!')
        print('响应:', response.text)
        return True

    except Exception as e:
        print('❌ API测试失败:', str(e))
        return False

if __name__ == "__main__":
    test_gemini_api()
