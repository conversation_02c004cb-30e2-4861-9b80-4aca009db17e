:root {
    --font-sans: 'Inter', -apple-system, sans-serif;
    --bg-color: #F9F9FB;
    --bubble-agent-bg: #EFEFF2;
    --bubble-user-bg: #0A84FF;
    --text-primary: #1d1d1f;
    --text-secondary: #8A8A8E;
    --text-on-accent: #ffffff;
    --border-color: #E5E5E5;
    --radius-lg: 22px; 
    --radius-md: 16px;
    --radius-sm: 12px; 
}


body { margin: 0; font-family: var(--font-sans); background-color: var(--bg-color); color: var(--text-primary); overflow: hidden; }
.initial-view, .chat-view { display: none; }
body.show-initial-view .initial-view { display: flex; }
body.show-chat-view .chat-view { display: flex; flex-direction: column; height: 100vh; }
.logo { height: clamp(40px, 7vw, 120px); width: auto; display: block; }


.language-switcher {
    position: fixed;
    top: 2.5rem;
    right: 2.5rem;
    z-index: 100;
    background: var(--bubble-agent-bg);
    border-radius: var(--radius-sm);
    padding: 4px;
    display: flex;
}
.language-switcher button {
    border: none;
    background-color: transparent;
    padding: 6px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 9px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease-in-out;
}
.language-switcher button.active {
    background-color: #fff;
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}


.initial-view { height: 100vh; flex-direction: column; justify-content: center; align-items: center; padding: 2rem; position: relative; box-sizing: border-box; }
#logo-initial { position: absolute; top: 2.5rem; left: 2.5rem; }
.initial-content { display: flex; flex-direction: column; justify-content: center; align-items: center; width: 100%; flex-grow: 1; text-align: center;}
.hero-group { text-align: center; max-width: 520px; width: 100%; }
.hero-group h1 { font-size: 2.2rem; font-weight: 100; line-height: 1.3; margin: 0 0 1rem 0; color: #333; }
.initial-form { position: relative; }

.initial-form input { width: 100%; height: 60px; padding: 0 60px 0 25px; border: 1px solid #DFDFDF; border-radius: var(--radius-sm); background-color: #fff; font-size: 1.1rem; box-sizing: border-box; transition: border-color 0.2s, box-shadow 0.2s; }
#initial-input::placeholder { font-weight: 300; }
.initial-form button { position: absolute; right: 10px; top: 50%; transform: translateY(-50%); width: 40px; height: 40px; background: transparent; border: none; border-radius: 10px; color: var(--text-secondary); cursor: pointer; display: grid; place-items: center; transition: background-color 0.2s, color 0.2s; }
.initial-form button:hover { background-color: var(--bubble-agent-bg); color: var(--text-primary); }


.initial-footer { margin-top: 2.5rem; display: flex; align-items: center; gap: 1rem; font-size: 1rem; font-weight: 500; }
.initial-footer a { color: var(--text-secondary); text-decoration: none; transition: color 0.2s; }
.initial-footer a:hover { color: var(--text-primary); }
.initial-footer span { color: var(--border-color); }


.chat-header { padding: 0.75rem 1.5rem; border-bottom: 1px solid var(--border-color); background: rgba(255,255,255,0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); display: flex; align-items: center; justify-content: space-between; }
#logo-chat { display: none; }
.new-chat-button { background: var(--bubble-agent-bg); color: var(--text-primary); border: none; border-radius: 8px; padding: 8px 12px; display: inline-flex; align-items: center; gap: 8px; font-size: 0.9rem; font-weight: 500; cursor: pointer; }
.chat-log { flex-grow: 1; overflow-y: auto; padding: 1.5rem; display: flex; flex-direction: column; gap: 1.25rem; }
.chat-footer { padding: 1rem; flex-shrink: 0; }
.input-container { position: relative; }
.input-container input { width: 100%; height: 50px; padding: 0 50px 0 20px; border: 1px solid var(--border-color); border-radius: var(--radius-md); font-size: 1rem; box-sizing: border-box; background-color: #fff; }
.submit-button { position: absolute; right: 8px; top: 50%; transform: translateY(-50%); width: 34px; height: 34px; background: var(--bubble-user-bg); border: none; border-radius: 50%; color: white; cursor: pointer; display: grid; place-items: center; transition: transform 0.2s; }


.message-group { max-width: 80%; width: fit-content; }
.message-group.user { align-self: flex-end; }
.message-group.agent { align-self: flex-start; }
.message-group.has-code, .message-group.has-player { max-width: 100%; width: 100%; }
.message-bubble { padding: 12px 18px; border-radius: var(--radius-lg); font-size: 1rem; line-height: 1.5; }
.user .message-bubble { background: var(--bubble-user-bg); color: var(--text-on-accent); border-bottom-right-radius: 6px; }
.status-bubble { display: flex; align-items: center; gap: 10px; padding: 12px 18px; border-radius: var(--radius-lg); background: var(--bubble-agent-bg); color: var(--text-secondary); }
.error-bubble { background-color: #FFF0F0; color: #D8000C; }
.icon-error { width: 20px; height: 20px; fill: currentColor; flex-shrink: 0; }
.thinking-dots { display: flex; gap: 4px; }
.thinking-dots span { width: 7px; height: 7px; background-color: currentColor; border-radius: 50%; animation: bounce 1.4s infinite both; }
.thinking-dots span:nth-child(2) { animation-delay: 0.2s; }
.thinking-dots span:nth-child(3) { animation-delay: 0.4s; }
@keyframes bounce { 0%, 80%, 100% { transform: scale(0); } 40% { transform: scale(1.0); } }


.code-details { border-radius: var(--radius-lg); background: #2C2C2E; overflow: hidden; }
.code-summary { display: flex; justify-content: space-between; align-items: center; padding: 14px 18px; cursor: pointer; user-select: none; list-style: none; }
.code-summary::-webkit-details-marker { display: none; }
.summary-title { display: flex; align-items: center; gap: 10px; font-weight: 500; color: #E0E0E0; }
.icon-code, .icon-chevron, .action-button svg { width: 1.2em; height: 1.2em; stroke: currentColor; stroke-width: 1.5; fill: none; stroke-linecap: round; stroke-linejoin: round; }
.icon-chevron { color: var(--text-secondary); transition: transform 0.2s; }
.code-details[open] .icon-chevron { transform: rotate(180deg); }
.code-content { padding: 0 1rem 1rem; }
.code-content pre { margin: 0; padding: 1rem; background: #1C1C1E; border-radius: 12px; color: #EBEBF599; font-family: 'SF Mono', 'Menlo', monospace; font-size: 0.9rem; line-height: 1.6; overflow-x: auto; }
.code-content code { white-space: pre-wrap; word-break: break-all; }
.code-content code span { display: inline; animation: tokenFadeIn 0.3s forwards; opacity: 0; }
@keyframes tokenFadeIn { to { opacity: 1; } }
.player-container { background: var(--bubble-agent-bg); border-radius: var(--radius-lg); padding: 8px; }
.iframe-wrapper { aspect-ratio: 16 / 9; width: 100%; max-width: calc(80vh * 16 / 9); max-height: 80vh; margin: 0 auto; border-radius: 12px; overflow: hidden; display: block; }
.animation-iframe { width: 100%; height: 100%; border: none; }
.player-actions { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; padding: 10px 4px 4px; }
.action-button { display: flex; align-items: center; justify-content: center; gap: 8px; padding: 10px; border: none; border-radius: 10px; font-size: 0.9rem; font-weight: 500; cursor: pointer; background: rgba(0,0,0,0.05); transition: background-color 0.2s; }
.action-button:hover { background: rgba(0,0,0,0.1); }
.action-button.open-new-window { background: rgba(10, 132, 255, 0.15); color: #0A84FF; }

/* === 动画占位符 === */
.animated-placeholder { position: absolute; top: 0; left: 25px; height: 60px; width: calc(100% - 85px); display: flex; align-items: center; overflow: hidden; pointer-events: none; }
.animated-placeholder.hidden { display: none; }
.animated-placeholder span { position: absolute; font-size: 1.1rem; font-weight: 300; color: var(--text-secondary); animation: scroll-up 4s ease-in-out forwards; }
@keyframes scroll-up {
    0% { opacity: 0; transform: translateY(60%); }
    15% { opacity: 1; transform: translateY(0%); }
    85% { opacity: 1; transform: translateY(0%); }
    100% { opacity: 0; transform: translateY(-60%); }
}

/* === 模态框样式 === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}
.modal-overlay.visible {
    display: flex;
    opacity: 1;
}
.modal-content {
    background: #fff;
    padding: 2rem 2.5rem;
    border-radius: var(--radius-md);
    max-width: 420px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    transform: scale(0.95);
    transition: transform 0.2s ease-in-out;
}
.modal-overlay.visible .modal-content {
    transform: scale(1);
}
.modal-content p {
    margin: 0 0 1.5rem 0;
    line-height: 1.6;
    color: var(--text-primary);
    font-size: 1rem;
}
.modal-button {
    width: 100%;
    padding: 12px;
    background: var(--bubble-user-bg);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}
.modal-button:hover {
    background-color: #0070E0;
}
.modal-close-button {
    position: absolute;
    top: 8px;
    right: 12px;
    background: transparent;
    border: none;
    font-size: 2rem;
    font-weight: 300;
    color: var(--border-color);
    cursor: pointer;
    line-height: 1;
    padding: 5px;
}
.modal-close-button:hover {
    color: var(--text-secondary);
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4); /* 半透明黑色 */
    z-index: 9998;
}

/* 警告框 */
.warning-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffecec;
    color: #b10000;
    padding: 24px 32px;
    border: 1px solid #f5c2c2;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 1rem;
    max-width: 90%;
    animation: fadeIn 0.3s ease-out;
}

/* 关闭按钮 */
.warning-box .close-button {
    background: none;
    border: none;
    font-size: 1.3rem;
    color: inherit;
    cursor: pointer;
    line-height: 1;
    padding: 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -40%); }
    to { opacity: 1; transform: translate(-50%, -50%); }
}
